#!/usr/bin/env python3
"""
Main demonstration script for the web_fetch library.

This script showcases the key features of the modern async web fetching utility.
"""

import asyncio
import json
from web_fetch import (
    ContentType,
    FetchConfig,
    FetchRequest,
    WebFetcher,
    fetch_url,
    fetch_urls,
)


async def demonstrate_features():
    """Demonstrate key features of the web_fetch library."""
    print("🚀 Web Fetch Library - Modern Async Web Scraping Demo")
    print("=" * 60)
    print()

    # 1. Simple single URL fetch
    print("1️⃣  Simple URL Fetch (JSON)")
    print("-" * 30)
    result = await fetch_url("https://httpbin.org/json", ContentType.JSON)
    if result.is_success:
        print(f"✅ Success! Status: {result.status_code}")
        print(f"📊 Response time: {result.response_time:.2f}s")
        if isinstance(result.content, dict):
            slideshow = result.content.get('slideshow', {})
            print(f"📄 Slideshow title: {slideshow.get('title', 'N/A')}")
    else:
        print(f"❌ Failed: {result.error}")
    print()

    # 2. Batch fetching with concurrency
    print("2️⃣  Batch Fetching (Multiple URLs)")
    print("-" * 35)
    urls = [
        "https://httpbin.org/get",
        "https://httpbin.org/user-agent",
        "https://httpbin.org/headers",
        "https://httpbin.org/ip",
    ]

    batch_result = await fetch_urls(urls, ContentType.JSON)
    print(f"📈 Total requests: {batch_result.total_requests}")
    print(f"✅ Successful: {batch_result.successful_requests}")
    print(f"❌ Failed: {batch_result.failed_requests}")
    print(f"📊 Success rate: {batch_result.success_rate:.1f}%")
    print(f"⏱️  Total time: {batch_result.total_time:.2f}s")
    print()

    # 3. Custom configuration and error handling
    print("3️⃣  Custom Configuration & Error Handling")
    print("-" * 42)

    config = FetchConfig(
        total_timeout=10.0,
        max_concurrent_requests=3,
        max_retries=2,
        retry_delay=0.5
    )

    # Test with a URL that will timeout
    request = FetchRequest(
        url="https://httpbin.org/delay/15",  # Will timeout
        content_type=ContentType.JSON
    )

    async with WebFetcher(config) as fetcher:
        result = await fetcher.fetch_single(request)

    print(f"🔄 Retry attempts: {result.retry_count}")
    print(f"⏱️  Response time: {result.response_time:.2f}s")
    if result.error:
        print(f"⚠️  Error handled: {result.error[:50]}...")
    print()

    # 4. HTML parsing
    print("4️⃣  HTML Content Parsing")
    print("-" * 25)

    html_result = await fetch_url("https://httpbin.org/html", ContentType.HTML)
    if html_result.is_success and isinstance(html_result.content, dict):
        print(f"📄 Page title: {html_result.content.get('title', 'N/A')}")
        print(f"🔗 Links found: {len(html_result.content.get('links', []))}")
        print(f"🖼️  Images found: {len(html_result.content.get('images', []))}")
        print(f"📝 Text preview: {html_result.content.get('text', '')[:100]}...")
    print()

    # 5. POST request with data
    print("5️⃣  POST Request with Data")
    print("-" * 25)

    post_data = {
        "library": "web_fetch",
        "version": "1.0.0",
        "features": ["async", "modern", "robust"]
    }

    post_request = FetchRequest(
        url="https://httpbin.org/post",
        method="POST",
        data=post_data,
        content_type=ContentType.JSON
    )

    async with WebFetcher() as fetcher:
        post_result = await fetcher.fetch_single(post_request)

    if post_result.is_success and isinstance(post_result.content, dict):
        echo_data = post_result.content.get('json', {})
        print(f"📤 Data sent successfully!")
        print(f"📥 Echo received: {echo_data.get('library', 'N/A')} v{echo_data.get('version', 'N/A')}")
    print()

    print("🎉 Demo completed! All modern Python features demonstrated:")
    print("   ✅ Async/await syntax")
    print("   ✅ Type hints with Union and Optional")
    print("   ✅ Context managers for resource cleanup")
    print("   ✅ Dataclasses and Pydantic models")
    print("   ✅ Pattern matching for content parsing")
    print("   ✅ Comprehensive error handling")
    print("   ✅ AIOHTTP best practices")


def main():
    """Main entry point."""
    try:
        asyncio.run(demonstrate_features())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    main()
