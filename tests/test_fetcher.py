"""
Unit tests for the fetcher module.

Tests for the WebFetcher class, async operations, and error handling.
"""

import asyncio
import json
import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import aiohttp
from aioresponses import aioresponses

from web_fetch.exceptions import (
    ConnectionError,
    HTTPError,
    TimeoutError,
    WebFetchError,
)
from web_fetch.fetcher import WebFetcher, fetch_url, fetch_urls
from web_fetch.models import (
    BatchFetchRequest,
    ContentType,
    FetchConfig,
    FetchRequest,
)


class TestWebFetcher:
    """Test the WebFetcher class."""
    
    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test WebFetcher as async context manager."""
        async with WebFetcher() as fetcher:
            assert fetcher._session is not None
            assert fetcher._semaphore is not None
        
        # Session should be closed after context exit
        assert fetcher._session is None
    
    @pytest.mark.asyncio
    async def test_session_creation(self):
        """Test session creation with custom config."""
        config = FetchConfig(
            total_timeout=60.0,
            max_concurrent_requests=5,
            verify_ssl=False
        )
        
        fetcher = WebFetcher(config)
        await fetcher._create_session()
        
        assert fetcher._session is not None
        assert fetcher._semaphore._value == 5  # Semaphore limit
        
        await fetcher.close()
    
    @pytest.mark.asyncio
    async def test_fetch_single_success(self):
        """Test successful single URL fetch."""
        with aioresponses() as m:
            m.get('https://example.com', payload={'message': 'success'}, status=200)
            
            request = FetchRequest(
                url='https://example.com',
                content_type=ContentType.JSON
            )
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert result.is_success
            assert result.status_code == 200
            assert isinstance(result.content, dict)
            assert result.content['message'] == 'success'
            assert result.error is None
    
    @pytest.mark.asyncio
    async def test_fetch_single_http_error(self):
        """Test HTTP error handling."""
        with aioresponses() as m:
            m.get('https://example.com/notfound', status=404)
            
            request = FetchRequest(url='https://example.com/notfound')
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert not result.is_success
            assert result.status_code == 404
            assert result.is_client_error
    
    @pytest.mark.asyncio
    async def test_fetch_single_with_retries(self):
        """Test retry logic on failures."""
        config = FetchConfig(max_retries=2, retry_delay=0.1)
        
        with aioresponses() as m:
            # First two attempts fail, third succeeds
            m.get('https://example.com', status=500)
            m.get('https://example.com', status=500)
            m.get('https://example.com', payload={'success': True}, status=200)
            
            request = FetchRequest(
                url='https://example.com',
                content_type=ContentType.JSON
            )
            
            async with WebFetcher(config) as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert result.is_success
            assert result.retry_count == 2
    
    @pytest.mark.asyncio
    async def test_fetch_batch(self):
        """Test batch fetching multiple URLs."""
        with aioresponses() as m:
            m.get('https://example.com/1', payload={'id': 1}, status=200)
            m.get('https://example.com/2', payload={'id': 2}, status=200)
            m.get('https://example.com/3', status=404)
            
            requests = [
                FetchRequest(url=f'https://example.com/{i}', content_type=ContentType.JSON)
                for i in range(1, 4)
            ]
            batch_request = BatchFetchRequest(requests=requests)
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_batch(batch_request)
            
            assert result.total_requests == 3
            assert result.successful_requests == 2
            assert result.failed_requests == 1
            assert result.success_rate == 66.7  # Approximately
    
    @pytest.mark.asyncio
    async def test_content_parsing_text(self):
        """Test text content parsing."""
        with aioresponses() as m:
            m.get('https://example.com', body='Hello, World!', status=200)
            
            request = FetchRequest(
                url='https://example.com',
                content_type=ContentType.TEXT
            )
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert result.content == 'Hello, World!'
    
    @pytest.mark.asyncio
    async def test_content_parsing_html(self):
        """Test HTML content parsing."""
        html_content = '''
        <html>
            <head><title>Test Page</title></head>
            <body>
                <h1>Hello</h1>
                <a href="https://example.com">Link</a>
                <img src="image.jpg" alt="Image">
            </body>
        </html>
        '''
        
        with aioresponses() as m:
            m.get('https://example.com', body=html_content, status=200)
            
            request = FetchRequest(
                url='https://example.com',
                content_type=ContentType.HTML
            )
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert isinstance(result.content, dict)
            assert result.content['title'] == 'Test Page'
            assert 'Hello' in result.content['text']
            assert 'https://example.com' in result.content['links']
            assert 'image.jpg' in result.content['images']
    
    @pytest.mark.asyncio
    async def test_content_parsing_json_error(self):
        """Test JSON parsing error handling."""
        with aioresponses() as m:
            m.get('https://example.com', body='invalid json', status=200)
            
            request = FetchRequest(
                url='https://example.com',
                content_type=ContentType.JSON
            )
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert not result.is_success
            assert 'Failed to parse JSON' in result.error
    
    @pytest.mark.asyncio
    async def test_post_request_with_data(self):
        """Test POST request with JSON data."""
        with aioresponses() as m:
            m.post('https://api.example.com/data', payload={'received': True}, status=201)
            
            request = FetchRequest(
                url='https://api.example.com/data',
                method='POST',
                data={'key': 'value'},
                content_type=ContentType.JSON
            )
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert result.status_code == 201
            assert result.content['received'] is True
    
    @pytest.mark.asyncio
    async def test_custom_headers(self):
        """Test custom headers in requests."""
        with aioresponses() as m:
            m.get('https://example.com', payload={'success': True}, status=200)
            
            request = FetchRequest(
                url='https://example.com',
                headers={'X-Custom-Header': 'test-value'},
                content_type=ContentType.JSON
            )
            
            async with WebFetcher() as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert result.is_success
    
    @pytest.mark.asyncio
    async def test_timeout_override(self):
        """Test timeout override in request."""
        config = FetchConfig(total_timeout=30.0)
        
        with aioresponses() as m:
            m.get('https://example.com', payload={'data': 'test'}, status=200)
            
            request = FetchRequest(
                url='https://example.com',
                timeout_override=5.0,
                content_type=ContentType.JSON
            )
            
            async with WebFetcher(config) as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert result.is_success
    
    @pytest.mark.asyncio
    async def test_response_size_limit(self):
        """Test response size limit enforcement."""
        config = FetchConfig(max_response_size=100)  # Very small limit
        large_content = 'x' * 200  # Exceeds limit
        
        with aioresponses() as m:
            m.get('https://example.com', body=large_content, status=200)
            
            request = FetchRequest(url='https://example.com')
            
            async with WebFetcher(config) as fetcher:
                result = await fetcher.fetch_single(request)
            
            assert not result.is_success
            assert 'exceeds maximum' in result.error


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    @pytest.mark.asyncio
    async def test_fetch_url(self):
        """Test fetch_url convenience function."""
        with aioresponses() as m:
            m.get('https://example.com', body='Hello, World!', status=200)
            
            result = await fetch_url('https://example.com', ContentType.TEXT)
            
            assert result.is_success
            assert result.content == 'Hello, World!'
    
    @pytest.mark.asyncio
    async def test_fetch_urls(self):
        """Test fetch_urls convenience function."""
        with aioresponses() as m:
            m.get('https://example.com/1', body='Page 1', status=200)
            m.get('https://example.com/2', body='Page 2', status=200)
            
            urls = ['https://example.com/1', 'https://example.com/2']
            result = await fetch_urls(urls, ContentType.TEXT)
            
            assert result.total_requests == 2
            assert result.successful_requests == 2
            assert result.success_rate == 100.0


class TestErrorScenarios:
    """Test various error scenarios."""
    
    @pytest.mark.asyncio
    async def test_connection_error(self):
        """Test connection error handling."""
        # Use a non-existent domain to trigger connection error
        request = FetchRequest(url='https://non-existent-domain-12345.com')
        
        async with WebFetcher() as fetcher:
            result = await fetcher.fetch_single(request)
        
        assert not result.is_success
        assert result.error is not None
    
    @pytest.mark.asyncio
    async def test_session_not_initialized(self):
        """Test error when session is not initialized."""
        fetcher = WebFetcher()
        request = FetchRequest(url='https://example.com')
        
        # Don't use context manager, so session won't be initialized
        result = await fetcher.fetch_single(request)
        
        assert not result.is_success
        assert 'not properly initialized' in result.error
