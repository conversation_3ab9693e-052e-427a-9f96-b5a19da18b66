# Web Fetch - Modern Async Web Scraping Utility

A robust, production-ready web fetching tool built with modern Python 3.11+ features and AIOHTTP for efficient asynchronous HTTP handling.

## 🚀 Features

### Modern Python Capabilities
- **Async/await syntax** for concurrent request handling
- **Type hints** with Union types and Optional for better code safety
- **Context managers** for proper resource cleanup
- **Dataclasses and Pydantic models** for structured data handling
- **Pattern matching** for content parsing (Python 3.10+)
- **Comprehensive error handling** with custom exception hierarchy

### AIOHTTP Best Practices
- **Session management** with proper connection pooling
- **Timeout configuration** (total, connect, read timeouts)
- **Retry logic** with exponential backoff
- **Concurrent request limiting** with semaphores
- **SSL verification** and custom headers support
- **Response size limits** for memory safety

### Content Processing
- **Multiple content types**: JSON, HTML, text, raw bytes
- **HTML parsing** with BeautifulSoup integration
- **Automatic encoding detection** for text content
- **Structured HTML data extraction** (title, links, images, text)

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd web-fetch

# Install dependencies (using uv)
uv sync

# Or install with pip
pip install -e .

# Install with test dependencies
pip install -e ".[test]"
```

## 🔧 Quick Start

### Simple URL Fetching

```python
import asyncio
from web_fetch import fetch_url, ContentType

async def main():
    # Fetch as JSON
    result = await fetch_url("https://httpbin.org/json", ContentType.JSON)
    if result.is_success:
        print(f"Status: {result.status_code}")
        print(f"Content: {result.content}")

    # Fetch as HTML with parsing
    html_result = await fetch_url("https://example.com", ContentType.HTML)
    if html_result.is_success:
        print(f"Title: {html_result.content['title']}")
        print(f"Links: {len(html_result.content['links'])}")

asyncio.run(main())
```

### Batch Fetching

```python
import asyncio
from web_fetch import fetch_urls, ContentType

async def main():
    urls = [
        "https://httpbin.org/get",
        "https://httpbin.org/json",
        "https://httpbin.org/html"
    ]

    result = await fetch_urls(urls, ContentType.TEXT)
    print(f"Success rate: {result.success_rate:.1f}%")
    print(f"Total time: {result.total_time:.2f}s")

asyncio.run(main())
```

### Advanced Usage with Custom Configuration

```python
import asyncio
from web_fetch import WebFetcher, FetchConfig, FetchRequest, ContentType

async def main():
    # Custom configuration
    config = FetchConfig(
        total_timeout=30.0,
        max_concurrent_requests=5,
        max_retries=3,
        retry_delay=1.0,
        verify_ssl=True
    )

    # Custom request with headers
    request = FetchRequest(
        url="https://api.example.com/data",
        method="POST",
        headers={"Authorization": "Bearer token"},
        data={"key": "value"},
        content_type=ContentType.JSON
    )

    async with WebFetcher(config) as fetcher:
        result = await fetcher.fetch_single(request)
        print(f"Response: {result.content}")

asyncio.run(main())
```

## 🖥️ Command Line Interface

The library includes a powerful CLI for quick web fetching:

```bash
# Simple URL fetch
web-fetch https://httpbin.org/json

# Fetch as JSON with custom timeout
web-fetch -t json --timeout 30 https://httpbin.org/json

# Batch fetch from file
web-fetch --batch urls.txt --concurrent 5

# POST request with data
web-fetch --method POST --data '{"key":"value"}' https://httpbin.org/post

# Save results to file
web-fetch -o results.json --format json https://example.com

# Custom headers
web-fetch --headers "Authorization: Bearer token" --headers "X-API-Key: key" https://api.example.com
```

### CLI Options

- `-t, --type`: Content type (text, json, html, raw)
- `--batch`: File containing URLs (one per line)
- `-o, --output`: Output file for results
- `--format`: Output format (json, summary, detailed)
- `--method`: HTTP method (GET, POST, PUT, DELETE, etc.)
- `--data`: Request data for POST/PUT requests
- `--headers`: Custom headers (can be used multiple times)
- `--timeout`: Request timeout in seconds
- `--concurrent`: Maximum concurrent requests
- `--retries`: Maximum retry attempts
- `--no-verify-ssl`: Disable SSL certificate verification
- `-v, --verbose`: Enable verbose output

## 📚 API Reference

### Core Classes

#### `WebFetcher`
Main async web fetcher class with session management.

```python
async with WebFetcher(config) as fetcher:
    result = await fetcher.fetch_single(request)
    batch_result = await fetcher.fetch_batch(batch_request)
```

#### `FetchConfig`
Configuration model with validation.

```python
config = FetchConfig(
    total_timeout=30.0,           # Total request timeout
    connect_timeout=10.0,         # Connection timeout
    read_timeout=20.0,            # Read timeout
    max_concurrent_requests=10,   # Concurrency limit
    max_retries=3,                # Retry attempts
    retry_delay=1.0,              # Base retry delay
    max_response_size=10*1024*1024,  # Max response size
    verify_ssl=True,              # SSL verification
    follow_redirects=True         # Follow redirects
)
```

#### `FetchRequest`
Request model with validation.

```python
request = FetchRequest(
    url="https://example.com",
    method="GET",                 # HTTP method
    headers={"Key": "Value"},     # Custom headers
    data={"key": "value"},        # Request data
    content_type=ContentType.JSON, # Content parsing type
    timeout_override=15.0         # Override default timeout
)
```

#### `FetchResult`
Result dataclass with response data and metadata.

```python
result = await fetcher.fetch_single(request)
print(f"Success: {result.is_success}")
print(f"Status: {result.status_code}")
print(f"Content: {result.content}")
print(f"Response time: {result.response_time}")
print(f"Error: {result.error}")
```

### Content Types

- `ContentType.TEXT`: Parse as UTF-8 text
- `ContentType.JSON`: Parse as JSON object
- `ContentType.HTML`: Parse HTML and extract structured data
- `ContentType.RAW`: Return raw bytes

### Exception Hierarchy

```
WebFetchError
├── NetworkError
├── TimeoutError
├── ConnectionError
├── HTTPError
│   ├── AuthenticationError (401, 403)
│   ├── NotFoundError (404)
│   ├── RateLimitError (429)
│   └── ServerError (5xx)
└── ContentError
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=web_fetch

# Run specific test file
pytest tests/test_fetcher.py

# Run with verbose output
pytest -v
```

### Test Categories

- **Unit tests**: Test individual components and functions
- **Integration tests**: Test with real HTTP requests (marked as `@pytest.mark.integration`)
- **Error scenario tests**: Test various failure modes and error handling

## 📁 Project Structure

```
web-fetch/
├── web_fetch/              # Main package
│   ├── __init__.py         # Package exports
│   ├── models.py           # Pydantic models and dataclasses
│   ├── fetcher.py          # Core WebFetcher implementation
│   ├── exceptions.py       # Custom exception hierarchy
│   └── cli.py              # Command-line interface
├── tests/                  # Test suite
│   ├── test_models.py      # Model tests
│   ├── test_fetcher.py     # Fetcher tests
│   └── test_exceptions.py  # Exception tests
├── examples/               # Usage examples
│   └── basic_usage.py      # Comprehensive examples
├── main.py                 # Demo script
├── pyproject.toml          # Project configuration
├── pytest.ini             # Test configuration
└── README.md               # This file
```

## 🔧 Configuration Examples

### Retry Strategies

```python
from web_fetch import FetchConfig, RetryStrategy

# Exponential backoff (default)
config = FetchConfig(
    retry_strategy=RetryStrategy.EXPONENTIAL,
    max_retries=3,
    retry_delay=1.0  # 1s, 2s, 4s delays
)

# Linear backoff
config = FetchConfig(
    retry_strategy=RetryStrategy.LINEAR,
    max_retries=3,
    retry_delay=1.0  # 1s, 2s, 3s delays
)

# No retries
config = FetchConfig(
    retry_strategy=RetryStrategy.NONE,
    max_retries=0
)
```

### Custom Headers

```python
from web_fetch import RequestHeaders, FetchConfig

# Default headers with custom additions
headers = RequestHeaders(
    user_agent="MyApp/1.0",
    custom_headers={
        "Authorization": "Bearer token",
        "X-API-Key": "secret-key"
    }
)

config = FetchConfig(headers=headers)
```

## 🚀 Performance Tips

1. **Use batch fetching** for multiple URLs to leverage concurrency
2. **Adjust concurrent request limits** based on target server capacity
3. **Configure appropriate timeouts** for your use case
4. **Use connection pooling** by reusing WebFetcher instances
5. **Choose appropriate content types** to avoid unnecessary parsing
6. **Set response size limits** to prevent memory issues

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [AIOHTTP](https://docs.aiohttp.org/) for async HTTP handling
- Uses [Pydantic](https://pydantic-docs.helpmanual.io/) for data validation
- HTML parsing powered by [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/)
- Inspired by modern Python async patterns and best practices