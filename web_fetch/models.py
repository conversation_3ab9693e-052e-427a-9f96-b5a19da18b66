"""
Data models and configuration classes for the web fetcher utility.

This module defines Pydantic models and dataclasses for structured data handling,
leveraging modern Python 3.11+ features including type hints, Union types, and Optional.
"""

from __future__ import annotations

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from pydantic import BaseModel, Field, HttpUrl, validator


class ContentType(str, Enum):
    """Enumeration of supported content types for fetched data."""
    
    RAW = "raw"
    JSON = "json"
    HTML = "html"
    TEXT = "text"


class RetryStrategy(str, Enum):
    """Enumeration of retry strategies for failed requests."""
    
    NONE = "none"
    LINEAR = "linear"
    EXPONENTIAL = "exponential"


@dataclass(frozen=True)
class RequestHeaders:
    """Immutable dataclass for HTTP request headers with common defaults."""
    
    user_agent: str = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    )
    accept: str = "*/*"
    accept_language: str = "en-US,en;q=0.9"
    accept_encoding: str = "gzip, deflate, br"
    connection: str = "keep-alive"
    custom_headers: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, str]:
        """Convert headers to dictionary format for aiohttp."""
        headers = {
            "User-Agent": self.user_agent,
            "Accept": self.accept,
            "Accept-Language": self.accept_language,
            "Accept-Encoding": self.accept_encoding,
            "Connection": self.connection,
        }
        headers.update(self.custom_headers)
        return headers


class FetchConfig(BaseModel):
    """Configuration model for web fetching operations using Pydantic."""
    
    # Timeout settings
    total_timeout: float = Field(default=30.0, gt=0, description="Total request timeout in seconds")
    connect_timeout: float = Field(default=10.0, gt=0, description="Connection timeout in seconds")
    read_timeout: float = Field(default=20.0, gt=0, description="Read timeout in seconds")
    
    # Concurrency settings
    max_concurrent_requests: int = Field(default=10, ge=1, le=100, description="Maximum concurrent requests")
    max_connections_per_host: int = Field(default=5, ge=1, le=20, description="Max connections per host")
    
    # Retry settings
    retry_strategy: RetryStrategy = Field(default=RetryStrategy.EXPONENTIAL)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1, le=60.0)
    
    # Content settings
    max_response_size: int = Field(default=10 * 1024 * 1024, gt=0, description="Max response size in bytes")
    follow_redirects: bool = Field(default=True)
    verify_ssl: bool = Field(default=True)
    
    # Headers
    headers: RequestHeaders = Field(default_factory=RequestHeaders)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True


class FetchRequest(BaseModel):
    """Model representing a single fetch request."""
    
    url: HttpUrl = Field(description="URL to fetch")
    method: str = Field(default="GET", regex=r"^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)$")
    headers: Optional[Dict[str, str]] = Field(default=None)
    data: Optional[Union[str, bytes, Dict[str, Any]]] = Field(default=None)
    params: Optional[Dict[str, str]] = Field(default=None)
    content_type: ContentType = Field(default=ContentType.RAW)
    timeout_override: Optional[float] = Field(default=None, gt=0)
    
    @validator('url')
    def validate_url(cls, v):
        """Validate URL format and scheme."""
        parsed = urlparse(str(v))
        if parsed.scheme not in ('http', 'https'):
            raise ValueError('URL must use http or https scheme')
        return v
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


@dataclass
class FetchResult:
    """Dataclass representing the result of a fetch operation."""
    
    url: str
    status_code: int
    headers: Dict[str, str]
    content: Union[str, bytes, Dict[str, Any], None]
    content_type: ContentType
    response_time: float
    timestamp: datetime
    error: Optional[str] = None
    retry_count: int = 0
    
    @property
    def is_success(self) -> bool:
        """Check if the request was successful."""
        return 200 <= self.status_code < 300 and self.error is None
    
    @property
    def is_client_error(self) -> bool:
        """Check if the request resulted in a client error (4xx)."""
        return 400 <= self.status_code < 500
    
    @property
    def is_server_error(self) -> bool:
        """Check if the request resulted in a server error (5xx)."""
        return 500 <= self.status_code < 600


class BatchFetchRequest(BaseModel):
    """Model for batch fetching multiple URLs."""
    
    requests: List[FetchRequest] = Field(min_items=1, max_items=1000)
    config: Optional[FetchConfig] = Field(default=None)
    
    @validator('requests')
    def validate_unique_urls(cls, v):
        """Ensure URLs are unique in batch request."""
        urls = [str(req.url) for req in v]
        if len(urls) != len(set(urls)):
            raise ValueError('Duplicate URLs found in batch request')
        return v


@dataclass
class BatchFetchResult:
    """Dataclass representing results from a batch fetch operation."""
    
    results: List[FetchResult]
    total_requests: int
    successful_requests: int
    failed_requests: int
    total_time: float
    timestamp: datetime
    
    @classmethod
    def from_results(cls, results: List[FetchResult], total_time: float) -> BatchFetchResult:
        """Create BatchFetchResult from a list of individual results."""
        successful = sum(1 for r in results if r.is_success)
        failed = len(results) - successful
        
        return cls(
            results=results,
            total_requests=len(results),
            successful_requests=successful,
            failed_requests=failed,
            total_time=total_time,
            timestamp=datetime.now()
        )
    
    @property
    def success_rate(self) -> float:
        """Calculate the success rate as a percentage."""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
