#!/usr/bin/env python3
"""
Command-line interface for the web_fetch library.

This module provides a simple CLI for fetching URLs with various options
and output formats.
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path
from typing import List, Optional

from . import (
    ContentType,
    FetchConfig,
    FetchRequest,
    WebFetcher,
    fetch_url,
    fetch_urls,
)


def create_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(
        description="Modern async web fetcher with AIOHTTP",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s https://httpbin.org/get
  %(prog)s -t json https://httpbin.org/json
  %(prog)s -t html -o output.json https://example.com
  %(prog)s --batch urls.txt
  %(prog)s --concurrent 5 --timeout 30 https://httpbin.org/delay/5
        """
    )
    
    # URL arguments
    parser.add_argument(
        'urls',
        nargs='*',
        help='URLs to fetch (or use --batch for file input)'
    )
    
    # Content type options
    parser.add_argument(
        '-t', '--type',
        choices=['text', 'json', 'html', 'raw'],
        default='text',
        help='Content type for parsing (default: text)'
    )
    
    # Input/Output options
    parser.add_argument(
        '--batch',
        type=Path,
        help='File containing URLs to fetch (one per line)'
    )
    
    parser.add_argument(
        '-o', '--output',
        type=Path,
        help='Output file for results (default: stdout)'
    )
    
    parser.add_argument(
        '--format',
        choices=['json', 'summary', 'detailed'],
        default='summary',
        help='Output format (default: summary)'
    )
    
    # Request configuration
    parser.add_argument(
        '--method',
        default='GET',
        help='HTTP method (default: GET)'
    )
    
    parser.add_argument(
        '--data',
        help='Request data (for POST/PUT requests)'
    )
    
    parser.add_argument(
        '--headers',
        action='append',
        help='Custom headers in format "Key: Value" (can be used multiple times)'
    )
    
    # Timing and concurrency
    parser.add_argument(
        '--timeout',
        type=float,
        default=30.0,
        help='Request timeout in seconds (default: 30)'
    )
    
    parser.add_argument(
        '--concurrent',
        type=int,
        default=10,
        help='Maximum concurrent requests (default: 10)'
    )
    
    parser.add_argument(
        '--retries',
        type=int,
        default=3,
        help='Maximum retry attempts (default: 3)'
    )
    
    # SSL and verification
    parser.add_argument(
        '--no-verify-ssl',
        action='store_true',
        help='Disable SSL certificate verification'
    )
    
    # Verbose output
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    
    return parser


def parse_headers(header_strings: Optional[List[str]]) -> dict:
    """Parse header strings into a dictionary."""
    headers = {}
    if header_strings:
        for header_str in header_strings:
            if ':' in header_str:
                key, value = header_str.split(':', 1)
                headers[key.strip()] = value.strip()
            else:
                print(f"Warning: Invalid header format: {header_str}", file=sys.stderr)
    return headers


def load_urls_from_file(file_path: Path) -> List[str]:
    """Load URLs from a text file."""
    try:
        with open(file_path, 'r') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        return urls
    except FileNotFoundError:
        print(f"Error: File not found: {file_path}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error reading file {file_path}: {e}", file=sys.stderr)
        sys.exit(1)


def format_output(results, format_type: str, verbose: bool = False) -> str:
    """Format results for output."""
    if format_type == 'json':
        # Convert results to JSON-serializable format
        if hasattr(results, 'results'):  # BatchFetchResult
            output = {
                'total_requests': results.total_requests,
                'successful_requests': results.successful_requests,
                'failed_requests': results.failed_requests,
                'success_rate': results.success_rate,
                'total_time': results.total_time,
                'results': []
            }
            for result in results.results:
                output['results'].append({
                    'url': result.url,
                    'status_code': result.status_code,
                    'success': result.is_success,
                    'response_time': result.response_time,
                    'content_length': len(str(result.content)) if result.content else 0,
                    'error': result.error,
                    'retry_count': result.retry_count
                })
        else:  # Single FetchResult
            output = {
                'url': results.url,
                'status_code': results.status_code,
                'success': results.is_success,
                'response_time': results.response_time,
                'content': results.content if verbose else None,
                'content_length': len(str(results.content)) if results.content else 0,
                'error': results.error,
                'retry_count': results.retry_count
            }
        return json.dumps(output, indent=2)
    
    elif format_type == 'summary':
        if hasattr(results, 'results'):  # BatchFetchResult
            output = []
            output.append(f"Batch Results Summary:")
            output.append(f"  Total requests: {results.total_requests}")
            output.append(f"  Successful: {results.successful_requests}")
            output.append(f"  Failed: {results.failed_requests}")
            output.append(f"  Success rate: {results.success_rate:.1f}%")
            output.append(f"  Total time: {results.total_time:.2f}s")
            output.append("")
            
            for i, result in enumerate(results.results, 1):
                status = "✓" if result.is_success else "✗"
                output.append(f"{i:3d}. {status} {result.url} ({result.status_code}) {result.response_time:.2f}s")
                if result.error:
                    output.append(f"     Error: {result.error}")
        else:  # Single FetchResult
            status = "✓" if results.is_success else "✗"
            output = [
                f"{status} {results.url}",
                f"Status: {results.status_code}",
                f"Response time: {results.response_time:.2f}s",
                f"Content length: {len(str(results.content)) if results.content else 0}"
            ]
            if results.error:
                output.append(f"Error: {results.error}")
        
        return "\n".join(output)
    
    elif format_type == 'detailed':
        # Similar to summary but with more details
        return format_output(results, 'summary', verbose) + "\n\nContent preview:\n" + str(results.content)[:500] + "..."


async def main():
    """Main CLI function."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Determine URLs to fetch
    urls = []
    if args.batch:
        urls = load_urls_from_file(args.batch)
    elif args.urls:
        urls = args.urls
    else:
        parser.print_help()
        sys.exit(1)
    
    if not urls:
        print("Error: No URLs to fetch", file=sys.stderr)
        sys.exit(1)
    
    # Parse content type
    content_type_map = {
        'text': ContentType.TEXT,
        'json': ContentType.JSON,
        'html': ContentType.HTML,
        'raw': ContentType.RAW
    }
    content_type = content_type_map[args.type]
    
    # Parse headers
    headers = parse_headers(args.headers)
    
    # Create configuration
    config = FetchConfig(
        total_timeout=args.timeout,
        max_concurrent_requests=args.concurrent,
        max_retries=args.retries,
        verify_ssl=not args.no_verify_ssl
    )
    
    if args.verbose:
        print(f"Fetching {len(urls)} URL(s) with {args.concurrent} max concurrent requests")
        print(f"Timeout: {args.timeout}s, Retries: {args.retries}")
        print()
    
    try:
        # Fetch URLs
        if len(urls) == 1:
            # Single URL
            request = FetchRequest(
                url=urls[0],
                method=args.method,
                headers=headers,
                data=args.data,
                content_type=content_type
            )
            
            async with WebFetcher(config) as fetcher:
                results = await fetcher.fetch_single(request)
        else:
            # Multiple URLs
            requests = [
                FetchRequest(
                    url=url,
                    method=args.method,
                    headers=headers,
                    data=args.data,
                    content_type=content_type
                )
                for url in urls
            ]
            
            from .models import BatchFetchRequest
            batch_request = BatchFetchRequest(requests=requests, config=config)
            
            async with WebFetcher(config) as fetcher:
                results = await fetcher.fetch_batch(batch_request)
        
        # Format and output results
        output = format_output(results, args.format, args.verbose)
        
        if args.output:
            with open(args.output, 'w') as f:
                f.write(output)
            if args.verbose:
                print(f"Results written to {args.output}")
        else:
            print(output)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
