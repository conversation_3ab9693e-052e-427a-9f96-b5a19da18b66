"""
Modern async web scraping/fetching utility with AIOHTTP.

This package provides a robust, production-ready web fetching tool that demonstrates
modern Python capabilities and efficient asynchronous HTTP handling.

Features:
- Async/await syntax for concurrent requests
- Modern Python 3.11+ features (type hints, pattern matching, dataclasses)
- AIOHTTP best practices with session management and connection pooling
- Comprehensive error handling and retry logic
- Structured data models with Pydantic
- Multiple content parsing options (JSON, HTML, text, raw)
"""

from .exceptions import (
    AuthenticationError,
    ConnectionError,
    ContentError,
    HTTPError,
    NetworkError,
    NotFoundError,
    RateLimitError,
    ServerError,
    TimeoutError,
    WebFetchError,
)
from .fetcher import WebFetcher, fetch_url, fetch_urls
from .models import (
    BatchFetchRequest,
    BatchFetchResult,
    ContentType,
    FetchConfig,
    FetchRequest,
    FetchResult,
    RequestHeaders,
    RetryStrategy,
)

__version__ = "0.1.0"
__author__ = "Web Fetch Team"
__email__ = "<EMAIL>"

__all__ = [
    # Main classes
    "WebFetcher",
    
    # Convenience functions
    "fetch_url",
    "fetch_urls",
    
    # Models and configuration
    "FetchConfig",
    "FetchRequest",
    "FetchResult",
    "BatchFetchRequest",
    "BatchFetchResult",
    "RequestHeaders",
    
    # Enums
    "ContentType",
    "RetryStrategy",
    
    # Exceptions
    "WebFetchError",
    "NetworkError",
    "TimeoutError",
    "ConnectionError",
    "HTTPError",
    "ContentError",
    "RateLimitError",
    "AuthenticationError",
    "NotFoundError",
    "ServerError",
]
